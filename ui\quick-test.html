<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MDAC插件快速测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
        
        .form-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .form-group {
            margin: 10px 0;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>🧪 MDAC AI助手快速测试</h1>
    
    <div class="test-card">
        <h2>系统状态检查</h2>
        <button class="test-button" onclick="checkSystemStatus()">检查系统状态</button>
        <button class="test-button" onclick="testBasicFunctions()">测试基础功能</button>
        <div id="systemStatus" class="test-result"></div>
    </div>

    <div class="test-card">
        <h2>表单测试区域</h2>
        <div class="form-section">
            <div>
                <h3>个人信息</h3>
                <div class="form-group">
                    <label>姓名</label>
                    <input type="text" id="name" placeholder="Full Name">
                </div>
                <div class="form-group">
                    <label>护照号码</label>
                    <input type="text" id="passportNo" placeholder="A12345678">
                </div>
                <div class="form-group">
                    <label>出生日期</label>
                    <input type="date" id="dateOfBirth">
                </div>
                <div class="form-group">
                    <label>国籍</label>
                    <select id="nationality">
                        <option value="">选择国籍</option>
                        <option value="CHN">中国</option>
                        <option value="USA">美国</option>
                        <option value="SGP">新加坡</option>
                    </select>
                </div>
            </div>
            <div>
                <h3>旅行信息</h3>
                <div class="form-group">
                    <label>到达日期</label>
                    <input type="date" id="arrivalDate">
                </div>
                <div class="form-group">
                    <label>航班号</label>
                    <input type="text" id="flightNo" placeholder="MH123">
                </div>
                <div class="form-group">
                    <label>州属</label>
                    <select id="state">
                        <option value="">选择州属</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>城市</label>
                    <select id="city">
                        <option value="">选择城市</option>
                    </select>
                </div>
            </div>
        </div>
        
        <button class="test-button" onclick="testFormFilling()">测试表单填充</button>
        <button class="test-button" onclick="testDataCollection()">测试数据收集</button>
        <button class="test-button" onclick="clearTestForm()">清除表单</button>
    </div>

    <div class="test-card">
        <h2>AI解析测试</h2>
        <div class="form-group">
            <label>个人信息输入</label>
            <textarea id="personalInfoInput" rows="4" placeholder="输入个人信息进行AI解析测试..."></textarea>
        </div>
        <button class="test-button" onclick="testAIParsing()">测试AI解析</button>
        <div id="aiParseResult" class="test-result"></div>
    </div>

    <div class="test-card">
        <h2>功能测试</h2>
        <button class="test-button" onclick="testDataValidation()">测试数据验证</button>
        <button class="test-button" onclick="testCityData()">测试城市数据</button>
        <button class="test-button" onclick="testStorageOperations()">测试存储操作</button>
        <div id="functionTestResult" class="test-result"></div>
    </div>

    <!-- 隐藏的元素用于测试 -->
    <div style="display: none;">
        <div id="connectionStatus"></div>
        <button id="parsePersonalBtn"></button>
        <button id="clearAllBtn"></button>
        <button id="updateToMDACBtn"></button>
        <input id="presetEmail" value="<EMAIL>">
        <input id="presetPhone" value="+60167372551">
    </div>

    <!-- 模拟Chrome API -->
    <script>
        if (typeof chrome === 'undefined') {
            window.chrome = {
                runtime: {
                    getURL: (path) => '../' + path
                },
                storage: {
                    local: {
                        get: async (keys) => {
                            console.log('模拟存储读取:', keys);
                            return {};
                        },
                        set: async (data) => {
                            console.log('模拟存储写入:', data);
                        }
                    }
                },
                tabs: {
                    create: async (options) => {
                        console.log('模拟打开标签页:', options);
                        return { id: 1 };
                    },
                    query: async (query) => {
                        console.log('模拟查询标签页:', query);
                        return [];
                    },
                    sendMessage: async (tabId, message) => {
                        console.log('模拟发送消息:', tabId, message);
                    }
                }
            };
        }
    </script>

    <!-- 加载主控制器 -->
    <script src="ui-sidepanel-main.js"></script>

    <!-- 测试脚本 -->
    <script>
        function checkSystemStatus() {
            const result = document.getElementById('systemStatus');
            let status = '';
            
            if (window.MDACMainController) {
                status += '✅ MDACMainController 类已定义\n';
            } else {
                status += '❌ MDACMainController 类未定义\n';
            }
            
            if (window.mdacController) {
                status += '✅ mdacController 实例已创建\n';
                status += `版本: ${window.mdacController.version}\n`;
                status += `初始化状态: ${window.mdacController.initialized ? '✅ 已初始化' : '❌ 未初始化'}\n`;
                
                const controllerStatus = window.mdacController.getStatus();
                status += `数据加载状态:\n`;
                status += `  - AI配置: ${controllerStatus.dataLoaded.aiConfig ? '✅' : '❌'}\n`;
                status += `  - 城市数据: ${controllerStatus.dataLoaded.cityData ? '✅' : '❌'}\n`;
                status += `  - 字段映射: ${controllerStatus.dataLoaded.fieldMappings ? '✅' : '❌'}\n`;
            } else {
                status += '❌ mdacController 实例未创建\n';
            }
            
            result.textContent = status;
        }

        function testBasicFunctions() {
            const result = document.getElementById('systemStatus');
            let status = result.textContent + '\n--- 基础功能测试 ---\n';
            
            if (window.mdacController) {
                try {
                    // 测试消息显示
                    window.mdacController.showMessage('info', '测试消息显示功能');
                    status += '✅ 消息显示功能正常\n';
                    
                    // 测试字段状态更新
                    window.mdacController.updateFieldStatus();
                    status += '✅ 字段状态更新功能正常\n';
                    
                    // 测试数据收集
                    const personalData = window.mdacController.collectPersonalData();
                    const travelData = window.mdacController.collectTravelData();
                    status += `✅ 数据收集功能正常 (个人: ${Object.keys(personalData).length}, 旅行: ${Object.keys(travelData).length})\n`;
                    
                } catch (error) {
                    status += `❌ 基础功能测试失败: ${error.message}\n`;
                }
            }
            
            result.textContent = status;
        }

        function testFormFilling() {
            if (window.mdacController) {
                const testData = {
                    name: 'John Doe',
                    passportNo: 'A1234567',
                    dateOfBirth: '1990-01-01',
                    nationality: 'CHN',
                    arrivalDate: '2025-08-01',
                    flightNo: 'MH123'
                };
                
                window.mdacController.fillPersonalFields(testData);
                window.mdacController.fillTravelFields(testData);
                window.mdacController.showMessage('success', '测试数据已填充到表单');
            }
        }

        function testDataCollection() {
            if (window.mdacController) {
                const personalData = window.mdacController.collectPersonalData();
                const travelData = window.mdacController.collectTravelData();
                
                const result = {
                    personal: personalData,
                    travel: travelData,
                    total: Object.keys(personalData).length + Object.keys(travelData).length
                };
                
                document.getElementById('functionTestResult').textContent = 
                    '数据收集结果:\n' + JSON.stringify(result, null, 2);
            }
        }

        function clearTestForm() {
            if (window.mdacController) {
                window.mdacController.clearAllData();
            }
        }

        function testAIParsing() {
            const input = document.getElementById('personalInfoInput').value;
            const result = document.getElementById('aiParseResult');
            
            if (window.mdacController && input) {
                try {
                    const parsed = window.mdacController.parsePersonalText(input);
                    result.textContent = 'AI解析结果:\n' + JSON.stringify(parsed, null, 2);
                    
                    // 自动填充解析结果
                    window.mdacController.fillPersonalFields(parsed);
                } catch (error) {
                    result.textContent = '解析失败: ' + error.message;
                }
            } else {
                result.textContent = '请输入要解析的文本';
            }
        }

        function testDataValidation() {
            if (window.mdacController) {
                const validation = window.mdacController.validateFormData();
                document.getElementById('functionTestResult').textContent = 
                    '数据验证结果:\n' + JSON.stringify(validation, null, 2);
            }
        }

        function testCityData() {
            if (window.mdacController && window.mdacController.cityData) {
                const cityCount = Object.keys(window.mdacController.cityData).length;
                let totalCities = 0;
                Object.values(window.mdacController.cityData).forEach(cities => {
                    if (Array.isArray(cities)) totalCities += cities.length;
                });
                
                document.getElementById('functionTestResult').textContent = 
                    `城市数据测试:\n州属数量: ${cityCount}\n城市总数: ${totalCities}`;
            }
        }

        function testStorageOperations() {
            if (window.mdacController) {
                window.mdacController.saveData().then(() => {
                    document.getElementById('functionTestResult').textContent = '✅ 存储操作测试完成';
                }).catch(error => {
                    document.getElementById('functionTestResult').textContent = '❌ 存储操作失败: ' + error.message;
                });
            }
        }

        // 页面加载完成后自动检查状态
        window.addEventListener('load', () => {
            setTimeout(() => {
                checkSystemStatus();
            }, 1000);
        });
    </script>
</body>
</html>
