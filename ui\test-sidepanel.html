<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MDAC AI助手 - 测试页面</title>
    <link rel="stylesheet" href="ui-sidepanel.css">
    <style>
        .test-container {
            max-width: 400px;
            margin: 20px auto;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: white;
        }
        .test-section {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
        }
        .test-button {
            margin: 5px;
            padding: 8px 16px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-result {
            margin-top: 10px;
            padding: 10px;
            background: #e9ecef;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h2>🧪 MDAC AI助手功能测试</h2>
        
        <div class="test-section">
            <h3>基础功能测试</h3>
            <button class="test-button" onclick="testInitialization()">测试初始化</button>
            <button class="test-button" onclick="testUIElements()">测试UI元素</button>
            <button class="test-button" onclick="testEventListeners()">测试事件监听</button>
            <div id="basicTestResult" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3>AI解析测试</h3>
            <textarea id="testPersonalInput" placeholder="输入测试个人信息..." rows="3" style="width: 100%; margin-bottom: 10px;"></textarea>
            <button class="test-button" onclick="testPersonalParsing()">测试个人信息解析</button>
            <div id="parseTestResult" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3>数据管理测试</h3>
            <button class="test-button" onclick="testDataCollection()">测试数据收集</button>
            <button class="test-button" onclick="testDataSaving()">测试数据保存</button>
            <button class="test-button" onclick="testDataClearing()">测试数据清除</button>
            <div id="dataTestResult" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3>控制器状态</h3>
            <button class="test-button" onclick="showControllerStatus()">显示状态</button>
            <button class="test-button" onclick="showDebugInfo()">调试信息</button>
            <div id="statusResult" class="test-result"></div>
        </div>
    </div>

    <!-- 简化的侧边栏HTML结构用于测试 -->
    <div style="display: none;">
        <div id="connectionStatus">测试连接状态</div>
        <input type="text" id="name" placeholder="姓名">
        <input type="text" id="passportNo" placeholder="护照号">
        <textarea id="personalInfoInput" placeholder="个人信息输入"></textarea>
        <button id="parsePersonalBtn">解析个人信息</button>
        <button id="clearAllBtn">清除全部</button>
        <button id="updateToMDACBtn">更新到MDAC</button>
    </div>

    <!-- 模拟Chrome扩展API -->
    <script>
        // 模拟Chrome扩展API用于测试
        if (typeof chrome === 'undefined') {
            window.chrome = {
                storage: {
                    local: {
                        get: async (keys) => {
                            console.log('模拟 chrome.storage.local.get:', keys);
                            return {};
                        },
                        set: async (data) => {
                            console.log('模拟 chrome.storage.local.set:', data);
                        }
                    }
                },
                tabs: {
                    create: async (options) => {
                        console.log('模拟 chrome.tabs.create:', options);
                        window.open(options.url, '_blank');
                    },
                    query: async (query) => {
                        console.log('模拟 chrome.tabs.query:', query);
                        return [];
                    },
                    sendMessage: async (tabId, message) => {
                        console.log('模拟 chrome.tabs.sendMessage:', tabId, message);
                    }
                }
            };
        }
    </script>

    <!-- 加载主控制器 -->
    <script src="ui-sidepanel-main.js"></script>

    <!-- 测试脚本 -->
    <script>
        function testInitialization() {
            const result = document.getElementById('basicTestResult');
            if (window.mdacController) {
                result.innerHTML = `
                    ✅ 控制器已创建<br>
                    版本: ${window.mdacController.version}<br>
                    初始化状态: ${window.mdacController.initialized ? '✅ 已初始化' : '❌ 未初始化'}
                `;
            } else {
                result.innerHTML = '❌ 控制器未创建';
            }
        }

        function testUIElements() {
            const result = document.getElementById('basicTestResult');
            if (window.mdacController && window.mdacController.elements) {
                const elements = window.mdacController.elements;
                const elementCount = Object.keys(elements).length;
                const validElements = Object.values(elements).filter(el => el !== null).length;
                
                result.innerHTML = `
                    UI元素总数: ${elementCount}<br>
                    有效元素: ${validElements}<br>
                    缓存状态: ${elementCount > 0 ? '✅ 已缓存' : '❌ 未缓存'}
                `;
            } else {
                result.innerHTML = '❌ UI元素未缓存';
            }
        }

        function testEventListeners() {
            const result = document.getElementById('basicTestResult');
            // 测试按钮点击
            const testBtn = document.getElementById('parsePersonalBtn');
            if (testBtn) {
                testBtn.click();
                result.innerHTML = '✅ 事件监听器测试完成（检查控制台）';
            } else {
                result.innerHTML = '❌ 测试按钮未找到';
            }
        }

        function testPersonalParsing() {
            const result = document.getElementById('parseTestResult');
            const input = document.getElementById('testPersonalInput');
            const testData = input.value || '姓名: John Doe\n护照: A1234567\n出生日期: 01/01/1990\n性别: 男';
            
            if (window.mdacController) {
                const parsed = window.mdacController.parsePersonalText(testData);
                result.innerHTML = `
                    测试数据: ${testData}<br>
                    解析结果: ${JSON.stringify(parsed, null, 2)}
                `;
            } else {
                result.innerHTML = '❌ 控制器不可用';
            }
        }

        function testDataCollection() {
            const result = document.getElementById('dataTestResult');
            if (window.mdacController) {
                const personalData = window.mdacController.collectPersonalData();
                const travelData = window.mdacController.collectTravelData();
                
                result.innerHTML = `
                    个人数据: ${JSON.stringify(personalData, null, 2)}<br>
                    旅行数据: ${JSON.stringify(travelData, null, 2)}
                `;
            } else {
                result.innerHTML = '❌ 控制器不可用';
            }
        }

        function testDataSaving() {
            const result = document.getElementById('dataTestResult');
            if (window.mdacController) {
                window.mdacController.saveData().then(() => {
                    result.innerHTML = '✅ 数据保存测试完成';
                }).catch(error => {
                    result.innerHTML = `❌ 数据保存失败: ${error.message}`;
                });
            } else {
                result.innerHTML = '❌ 控制器不可用';
            }
        }

        function testDataClearing() {
            const result = document.getElementById('dataTestResult');
            if (window.mdacController) {
                window.mdacController.clearAllData();
                result.innerHTML = '✅ 数据清除测试完成';
            } else {
                result.innerHTML = '❌ 控制器不可用';
            }
        }

        function showControllerStatus() {
            const result = document.getElementById('statusResult');
            if (window.mdacController) {
                const status = window.mdacController.getStatus();
                result.innerHTML = `<pre>${JSON.stringify(status, null, 2)}</pre>`;
            } else {
                result.innerHTML = '❌ 控制器不可用';
            }
        }

        function showDebugInfo() {
            const result = document.getElementById('statusResult');
            if (window.mdacController) {
                window.mdacController.debug();
                result.innerHTML = '✅ 调试信息已输出到控制台';
            } else {
                result.innerHTML = '❌ 控制器不可用';
            }
        }

        // 页面加载完成后自动运行基础测试
        window.addEventListener('load', () => {
            setTimeout(() => {
                testInitialization();
                testUIElements();
            }, 1000);
        });
    </script>
</body>
</html>
