/**
 * MDAC AI智能助手 - 主控制器
 * 统一管理所有UI交互和核心功能
 * 创建日期: 2025-07-12
 * 版本: 3.0.0 (重构版本)
 */

(function() {
    'use strict';

    // 防止重复加载
    if (window.MDACMainController) {
        console.log('✅ [MDACMainController] 已存在，跳过重复加载');
        return;
    }

    class MDACMainController {
        constructor() {
            this.version = '3.0.0';
            this.initialized = false;
            this.data = {
                personal: {},
                travel: {},
                settings: {}
            };
            this.elements = {};
            this.aiConfig = null;
            this.cityData = null;
            this.fieldMappings = null;
        }

        /**
         * 初始化主控制器
         */
        async initialize() {
            try {
                console.log('🚀 [MDACMainController] 开始初始化...');
                
                // 1. 等待DOM加载完成
                if (document.readyState === 'loading') {
                    await new Promise(resolve => {
                        document.addEventListener('DOMContentLoaded', resolve);
                    });
                }

                // 2. 获取所有UI元素
                this.cacheElements();

                // 3. 加载配置和数据
                await this.loadConfigurations();

                // 4. 设置事件监听器
                this.setupEventListeners();

                // 5. 初始化UI状态
                this.initializeUI();

                // 6. 加载保存的数据
                await this.loadSavedData();
                await this.loadPresetData();

                this.initialized = true;
                this.updateConnectionStatus('connected', '🟢 MDAC AI助手已就绪');
                console.log('✅ [MDACMainController] 初始化完成');

                // 显示成功消息
                this.showMessage('success', 'MDAC AI助手已成功加载！');

            } catch (error) {
                console.error('❌ [MDACMainController] 初始化失败:', error);
                this.updateConnectionStatus('error', '🔴 初始化失败');
                this.showMessage('error', '初始化失败: ' + error.message);
            }
        }

        /**
         * 缓存所有UI元素
         */
        cacheElements() {
            console.log('📦 [MDACMainController] 缓存UI元素...');

            // 状态元素
            this.elements.connectionStatus = document.getElementById('connectionStatus');
            this.elements.aiStatus = document.getElementById('aiStatus');

            // 按钮元素
            this.elements.mdacAccessBtn = document.getElementById('mdacAccessBtn');
            this.elements.imageUploadBtn = document.getElementById('imageUploadBtn');
            this.elements.imageInput = document.getElementById('imageInput');
            this.elements.clearAllBtn = document.getElementById('clearAllBtn');
            this.elements.previewBtn = document.getElementById('previewBtn');
            this.elements.cityViewerBtn = document.getElementById('cityViewerBtn');
            this.elements.updateToMDACBtn = document.getElementById('updateToMDACBtn');

            // 解析相关元素
            this.elements.personalInfoInput = document.getElementById('personalInfoInput');
            this.elements.travelInfoInput = document.getElementById('travelInfoInput');
            this.elements.parsePersonalBtn = document.getElementById('parsePersonalBtn');
            this.elements.parseTravelBtn = document.getElementById('parseTravelBtn');

            // 表单字段元素
            this.elements.formFields = {
                // 个人信息字段
                name: document.getElementById('name'),
                passportNo: document.getElementById('passportNo'),
                dateOfBirth: document.getElementById('dateOfBirth'),
                nationality: document.getElementById('nationality'),
                sex: document.getElementById('sex'),
                passportExpiry: document.getElementById('passportExpiry'),
                
                // 旅行信息字段
                arrivalDate: document.getElementById('arrivalDate'),
                departureDate: document.getElementById('departureDate'),
                flightNo: document.getElementById('flightNo'),
                modeOfTravel: document.getElementById('modeOfTravel'),
                accommodation: document.getElementById('accommodation'),
                address: document.getElementById('address'),
                state: document.getElementById('state'),
                city: document.getElementById('city'),
                postcode: document.getElementById('postcode')
            };

            // 预设字段
            this.elements.presetEmail = document.getElementById('presetEmail');
            this.elements.presetPhone = document.getElementById('presetPhone');

            // 城市查看器元素
            this.elements.cityViewerArea = document.getElementById('cityViewerArea');
            this.elements.closeViewerBtn = document.getElementById('closeViewerBtn');
            this.elements.citySearchInput = document.getElementById('citySearchInput');
            this.elements.citySearchBtn = document.getElementById('citySearchBtn');
            this.elements.stateFilter = document.getElementById('stateFilter');
            this.elements.cityList = document.getElementById('cityList');

            // 模态框元素
            this.elements.modal = document.getElementById('modal');
            this.elements.modalTitle = document.getElementById('modalTitle');
            this.elements.modalBody = document.getElementById('modalBody');
            this.elements.modalClose = document.getElementById('modalClose');
            this.elements.modalCancel = document.getElementById('modalCancel');
            this.elements.modalConfirm = document.getElementById('modalConfirm');

            console.log('✅ [MDACMainController] UI元素缓存完成');
        }

        /**
         * 加载配置和数据文件
         */
        async loadConfigurations() {
            console.log('📋 [MDACMainController] 加载配置文件...');

            try {
                // 获取扩展根目录路径
                const extensionUrl = chrome.runtime ? chrome.runtime.getURL('') : '../';

                // 加载AI配置
                try {
                    const aiConfigResponse = await fetch(extensionUrl + 'config/enhanced-ai-config.js');
                    if (aiConfigResponse.ok) {
                        const aiConfigText = await aiConfigResponse.text();
                        this.aiConfig = { loaded: true, content: aiConfigText };
                        console.log('✅ AI配置加载成功');
                    }
                } catch (error) {
                    console.warn('⚠️ AI配置加载失败:', error);
                    this.aiConfig = { loaded: false };
                }

                // 加载城市数据
                try {
                    const cityDataResponse = await fetch(extensionUrl + 'config/malaysia-states-cities.json');
                    if (cityDataResponse.ok) {
                        this.cityData = await cityDataResponse.json();
                        console.log('✅ 城市数据加载成功，包含', Object.keys(this.cityData).length, '个州属');
                    }
                } catch (error) {
                    console.warn('⚠️ 城市数据加载失败:', error);
                    this.cityData = this.getDefaultCityData();
                }

                // 加载字段映射
                try {
                    const mappingResponse = await fetch(extensionUrl + 'config/mdac-official-mappings.json');
                    if (mappingResponse.ok) {
                        this.fieldMappings = await mappingResponse.json();
                        console.log('✅ 字段映射加载成功');
                    }
                } catch (error) {
                    console.warn('⚠️ 字段映射加载失败:', error);
                    this.fieldMappings = this.getDefaultFieldMappings();
                }

            } catch (error) {
                console.warn('⚠️ 配置文件加载失败:', error);
                // 使用默认配置
                this.aiConfig = { loaded: false };
                this.cityData = this.getDefaultCityData();
                this.fieldMappings = this.getDefaultFieldMappings();
            }
        }

        /**
         * 获取默认城市数据
         */
        getDefaultCityData() {
            return {
                "Kuala Lumpur": [
                    { name: "Kuala Lumpur", code: "KL", postcode: "50000" }
                ],
                "Selangor": [
                    { name: "Shah Alam", code: "SA", postcode: "40000" },
                    { name: "Petaling Jaya", code: "PJ", postcode: "46000" }
                ],
                "Penang": [
                    { name: "George Town", code: "GT", postcode: "10000" }
                ],
                "Johor": [
                    { name: "Johor Bahru", code: "JB", postcode: "80000" }
                ]
            };
        }

        /**
         * 获取默认字段映射
         */
        getDefaultFieldMappings() {
            return {
                name: "fullName",
                passportNo: "passportNumber",
                dateOfBirth: "birthDate",
                nationality: "nationality",
                sex: "gender",
                arrivalDate: "arrivalDate",
                departureDate: "departureDate",
                flightNo: "flightNumber",
                address: "accommodationAddress",
                state: "state",
                city: "city",
                postcode: "postcode"
            };
        }

        /**
         * 设置所有事件监听器
         */
        setupEventListeners() {
            console.log('🎯 [MDACMainController] 设置事件监听器...');

            // MDAC网站访问按钮
            if (this.elements.mdacAccessBtn) {
                this.elements.mdacAccessBtn.addEventListener('click', () => {
                    this.openMDACWebsite();
                });
            }

            // 图片上传按钮
            if (this.elements.imageUploadBtn) {
                this.elements.imageUploadBtn.addEventListener('click', () => {
                    this.elements.imageInput?.click();
                });
            }

            if (this.elements.imageInput) {
                this.elements.imageInput.addEventListener('change', (e) => {
                    this.handleImageUpload(e);
                });
            }

            // AI解析按钮
            if (this.elements.parsePersonalBtn) {
                this.elements.parsePersonalBtn.addEventListener('click', () => {
                    this.parsePersonalInfo();
                });
            }

            if (this.elements.parseTravelBtn) {
                this.elements.parseTravelBtn.addEventListener('click', () => {
                    this.parseTravelInfo();
                });
            }

            // 主要操作按钮
            if (this.elements.updateToMDACBtn) {
                this.elements.updateToMDACBtn.addEventListener('click', () => {
                    this.updateToMDAC();
                });
            }

            if (this.elements.clearAllBtn) {
                this.elements.clearAllBtn.addEventListener('click', () => {
                    this.clearAllData();
                });
            }

            if (this.elements.previewBtn) {
                this.elements.previewBtn.addEventListener('click', () => {
                    this.showDataPreview();
                });
            }

            // 城市查看器
            if (this.elements.cityViewerBtn) {
                this.elements.cityViewerBtn.addEventListener('click', () => {
                    this.toggleCityViewer();
                });
            }

            if (this.elements.closeViewerBtn) {
                this.elements.closeViewerBtn.addEventListener('click', () => {
                    this.closeCityViewer();
                });
            }

            // 模态框
            if (this.elements.modalClose) {
                this.elements.modalClose.addEventListener('click', () => {
                    this.hideModal();
                });
            }

            if (this.elements.modalCancel) {
                this.elements.modalCancel.addEventListener('click', () => {
                    this.hideModal();
                });
            }

            // 表单字段变化监听
            Object.values(this.elements.formFields).forEach(field => {
                if (field) {
                    field.addEventListener('input', () => {
                        this.updateFieldStatus();
                        this.autoSaveData(); // 自动保存
                    });
                }
            });

            // 预设字段监听
            if (this.elements.presetEmail) {
                this.elements.presetEmail.addEventListener('input', () => {
                    this.savePresetData();
                });
            }

            if (this.elements.presetPhone) {
                this.elements.presetPhone.addEventListener('input', () => {
                    this.savePresetData();
                });
            }

            // 城市搜索监听
            if (this.elements.citySearchBtn) {
                this.elements.citySearchBtn.addEventListener('click', () => {
                    this.searchCities();
                });
            }

            if (this.elements.citySearchInput) {
                this.elements.citySearchInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        this.searchCities();
                    }
                });
            }

            if (this.elements.stateFilter) {
                this.elements.stateFilter.addEventListener('change', (e) => {
                    this.displayCities(e.target.value);
                });
            }

            // 键盘快捷键
            document.addEventListener('keydown', (e) => {
                if (e.ctrlKey || e.metaKey) {
                    switch (e.key) {
                        case 's':
                            e.preventDefault();
                            this.saveData();
                            break;
                        case 'Enter':
                            if (e.shiftKey) {
                                e.preventDefault();
                                this.updateToMDAC();
                            }
                            break;
                    }
                }
            });

            console.log('✅ [MDACMainController] 事件监听器设置完成');
        }

        /**
         * 初始化UI状态
         */
        initializeUI() {
            console.log('🎨 [MDACMainController] 初始化UI状态...');

            // 设置预设值
            if (this.elements.presetEmail) {
                this.elements.presetEmail.value = '<EMAIL>';
            }
            if (this.elements.presetPhone) {
                this.elements.presetPhone.value = '+60167372551';
            }

            // 初始化州属和城市选择器
            this.initializeLocationSelectors();

            // 更新状态指示器
            this.updateFieldStatus();

            console.log('✅ [MDACMainController] UI状态初始化完成');
        }

        /**
         * 更新连接状态
         */
        updateConnectionStatus(status, message) {
            if (this.elements.connectionStatus) {
                this.elements.connectionStatus.textContent = message;
                this.elements.connectionStatus.className = `connection-status ${status}`;
            }
        }

        /**
         * 显示消息提示
         */
        showMessage(type, message, duration = 3000) {
            // 创建消息元素
            const messageEl = document.createElement('div');
            messageEl.className = `message ${type}`;
            messageEl.textContent = message;

            // 添加到页面
            document.body.appendChild(messageEl);

            // 自动移除
            setTimeout(() => {
                if (messageEl.parentNode) {
                    messageEl.parentNode.removeChild(messageEl);
                }
            }, duration);
        }

        /**
         * 打开MDAC网站
         */
        async openMDACWebsite() {
            try {
                const url = 'https://imigresen-online.imi.gov.my/mdac/main?registerMain';
                await chrome.tabs.create({ url: url });
                this.showMessage('success', 'MDAC网站已在新标签页中打开');
            } catch (error) {
                console.error('打开MDAC网站失败:', error);
                this.showMessage('error', '无法打开MDAC网站');
            }
        }

        /**
         * 处理图片上传
         */
        async handleImageUpload(event) {
            const file = event.target.files[0];
            if (!file) return;

            try {
                this.showMessage('info', '正在处理图片...');

                // 读取图片文件
                const imageData = await this.readFileAsDataURL(file);

                // 这里应该调用AI图片识别服务
                // 暂时显示占位符消息
                this.showMessage('success', '图片上传成功，AI识别功能开发中...');

            } catch (error) {
                console.error('图片处理失败:', error);
                this.showMessage('error', '图片处理失败');
            }
        }

        /**
         * 读取文件为DataURL
         */
        readFileAsDataURL(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = () => resolve(reader.result);
                reader.onerror = reject;
                reader.readAsDataURL(file);
            });
        }

        /**
         * 解析个人信息
         */
        async parsePersonalInfo() {
            const inputText = this.elements.personalInfoInput?.value?.trim();
            if (!inputText) {
                this.showMessage('warning', '请输入个人信息内容');
                return;
            }

            try {
                this.showMessage('info', 'AI正在解析个人信息...');

                // 简单的文本解析逻辑（后续可以集成真正的AI）
                const parsedData = this.parsePersonalText(inputText);

                // 填充表单字段
                this.fillPersonalFields(parsedData);

                this.showMessage('success', '个人信息解析完成');

            } catch (error) {
                console.error('个人信息解析失败:', error);
                this.showMessage('error', '个人信息解析失败');
            }
        }

        /**
         * 解析旅行信息
         */
        async parseTravelInfo() {
            const inputText = this.elements.travelInfoInput?.value?.trim();
            if (!inputText) {
                this.showMessage('warning', '请输入旅行信息内容');
                return;
            }

            try {
                this.showMessage('info', 'AI正在解析旅行信息...');

                // 简单的文本解析逻辑
                const parsedData = this.parseTravelText(inputText);

                // 填充表单字段
                this.fillTravelFields(parsedData);

                this.showMessage('success', '旅行信息解析完成');

            } catch (error) {
                console.error('旅行信息解析失败:', error);
                this.showMessage('error', '旅行信息解析失败');
            }
        }

        /**
         * 简单的个人信息文本解析
         */
        parsePersonalText(text) {
            const data = {};

            // 姓名匹配
            const nameMatch = text.match(/(?:姓名|名字|Name)[：:\s]*([A-Za-z\s]+)/i);
            if (nameMatch) data.name = nameMatch[1].trim();

            // 护照号匹配
            const passportMatch = text.match(/(?:护照|Passport)[：:\s]*([A-Z0-9]+)/i);
            if (passportMatch) data.passportNo = passportMatch[1].trim();

            // 出生日期匹配
            const birthMatch = text.match(/(?:出生|生日|Birth)[：:\s]*(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{4})/i);
            if (birthMatch) data.dateOfBirth = this.formatDate(birthMatch[1]);

            // 性别匹配
            const genderMatch = text.match(/(?:性别|Gender)[：:\s]*(男|女|Male|Female|M|F)/i);
            if (genderMatch) {
                const gender = genderMatch[1].toLowerCase();
                data.sex = (gender === '男' || gender === 'male' || gender === 'm') ? '1' : '2';
            }

            return data;
        }

        /**
         * 简单的旅行信息文本解析
         */
        parseTravelText(text) {
            const data = {};

            // 航班号匹配
            const flightMatch = text.match(/(?:航班|Flight)[：:\s]*([A-Z0-9]+)/i);
            if (flightMatch) data.flightNo = flightMatch[1].trim();

            // 到达日期匹配
            const arrivalMatch = text.match(/(?:到达|抵达|Arrival)[：:\s]*(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{4})/i);
            if (arrivalMatch) data.arrivalDate = this.formatDate(arrivalMatch[1]);

            // 离开日期匹配
            const departureMatch = text.match(/(?:离开|出发|Departure)[：:\s]*(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{4})/i);
            if (departureMatch) data.departureDate = this.formatDate(departureMatch[1]);

            // 地址匹配
            const addressMatch = text.match(/(?:地址|住址|Address)[：:\s]*([^\n\r]+)/i);
            if (addressMatch) data.address = addressMatch[1].trim();

            return data;
        }

        /**
         * 格式化日期为YYYY-MM-DD格式
         */
        formatDate(dateStr) {
            try {
                const parts = dateStr.split(/[\/\-]/);
                if (parts.length === 3) {
                    // 假设输入格式为DD/MM/YYYY或DD-MM-YYYY
                    const day = parts[0].padStart(2, '0');
                    const month = parts[1].padStart(2, '0');
                    const year = parts[2];
                    return `${year}-${month}-${day}`;
                }
            } catch (error) {
                console.error('日期格式化失败:', error);
            }
            return dateStr;
        }

        /**
         * 填充个人信息字段
         */
        fillPersonalFields(data) {
            Object.keys(data).forEach(key => {
                const field = this.elements.formFields[key];
                if (field && data[key]) {
                    field.value = data[key];
                    this.data.personal[key] = data[key];
                }
            });
            this.updateFieldStatus();
        }

        /**
         * 填充旅行信息字段
         */
        fillTravelFields(data) {
            Object.keys(data).forEach(key => {
                const field = this.elements.formFields[key];
                if (field && data[key]) {
                    field.value = data[key];
                    this.data.travel[key] = data[key];
                }
            });
            this.updateFieldStatus();
        }

        /**
         * 更新字段状态指示器
         */
        updateFieldStatus() {
            let filledCount = 0;
            const totalFields = Object.keys(this.elements.formFields).length;

            Object.entries(this.elements.formFields).forEach(([key, field]) => {
                if (field) {
                    const statusEl = document.querySelector(`[data-field="${key}"]`);
                    if (field.value && field.value.trim()) {
                        field.classList.add('filled');
                        if (statusEl) {
                            statusEl.className = 'field-status success';
                        }
                        filledCount++;
                    } else {
                        field.classList.remove('filled');
                        if (statusEl) {
                            statusEl.className = 'field-status';
                        }
                    }
                }
            });

            // 更新状态指示器
            const fieldsStatusEl = document.getElementById('fieldsStatus');
            if (fieldsStatusEl) {
                fieldsStatusEl.querySelector('.status-text').textContent = `字段: ${filledCount}/${totalFields}`;
            }

            // 更新完整度
            const completenessEl = document.getElementById('completenessStatus');
            if (completenessEl) {
                const percentage = Math.round((filledCount / totalFields) * 100);
                completenessEl.querySelector('.status-text').textContent = `完整度: ${percentage}%`;
            }
        }

        /**
         * 清除所有数据
         */
        clearAllData() {
            // 清除表单字段
            Object.values(this.elements.formFields).forEach(field => {
                if (field) {
                    field.value = '';
                    field.classList.remove('filled');
                }
            });

            // 清除输入区域
            if (this.elements.personalInfoInput) {
                this.elements.personalInfoInput.value = '';
            }
            if (this.elements.travelInfoInput) {
                this.elements.travelInfoInput.value = '';
            }

            // 清除数据
            this.data.personal = {};
            this.data.travel = {};

            // 更新状态
            this.updateFieldStatus();
            this.showMessage('success', '所有数据已清除');
        }

        /**
         * 显示数据预览
         */
        showDataPreview() {
            const personalData = this.collectPersonalData();
            const travelData = this.collectTravelData();

            const previewContent = this.generatePreviewHTML(personalData, travelData);

            this.showModal('数据预览', previewContent);
        }

        /**
         * 收集个人信息数据
         */
        collectPersonalData() {
            const data = {};
            const personalFields = ['name', 'passportNo', 'dateOfBirth', 'nationality', 'sex', 'passportExpiry'];

            personalFields.forEach(field => {
                const element = this.elements.formFields[field];
                if (element && element.value) {
                    data[field] = element.value;
                }
            });

            return data;
        }

        /**
         * 收集旅行信息数据
         */
        collectTravelData() {
            const data = {};
            const travelFields = ['arrivalDate', 'departureDate', 'flightNo', 'modeOfTravel',
                                'accommodation', 'address', 'state', 'city', 'postcode'];

            travelFields.forEach(field => {
                const element = this.elements.formFields[field];
                if (element && element.value) {
                    data[field] = element.value;
                }
            });

            return data;
        }

        /**
         * 生成预览HTML
         */
        generatePreviewHTML(personalData, travelData) {
            let html = '<div class="data-preview-container">';

            // 个人信息部分
            html += '<div class="preview-category">';
            html += '<h4>👤 个人信息</h4>';
            Object.entries(personalData).forEach(([key, value]) => {
                const label = this.getFieldLabel(key);
                html += `<div class="preview-item"><strong>${label}:</strong> <span class="preview-value">${value}</span></div>`;
            });
            html += '</div>';

            // 旅行信息部分
            html += '<div class="preview-category">';
            html += '<h4>✈️ 旅行信息</h4>';
            Object.entries(travelData).forEach(([key, value]) => {
                const label = this.getFieldLabel(key);
                html += `<div class="preview-item"><strong>${label}:</strong> <span class="preview-value">${value}</span></div>`;
            });
            html += '</div>';

            html += '</div>';
            return html;
        }

        /**
         * 获取字段标签
         */
        getFieldLabel(fieldName) {
            const labels = {
                name: '姓名',
                passportNo: '护照号码',
                dateOfBirth: '出生日期',
                nationality: '国籍',
                sex: '性别',
                passportExpiry: '护照到期日',
                arrivalDate: '到达日期',
                departureDate: '离开日期',
                flightNo: '航班号',
                modeOfTravel: '交通方式',
                accommodation: '住宿类型',
                address: '住宿地址',
                state: '州属',
                city: '城市',
                postcode: '邮政编码'
            };
            return labels[fieldName] || fieldName;
        }

        /**
         * 更新到MDAC页面
         */
        async updateToMDAC() {
            try {
                this.showMessage('info', '正在验证和更新数据...');

                // 验证表单数据
                const validation = this.validateFormData();
                if (!this.showValidationResult(validation)) {
                    return;
                }

                // 收集所有数据
                const personalData = this.collectPersonalData();
                const travelData = this.collectTravelData();
                const allData = { ...personalData, ...travelData };

                // 检查是否有数据
                if (Object.keys(allData).length === 0) {
                    this.showMessage('warning', '没有数据可以更新');
                    return;
                }

                this.showMessage('info', '正在发送数据到MDAC页面...');

                // 发送消息到content script
                const tabs = await chrome.tabs.query({ url: '*://imigresen-online.imi.gov.my/*' });
                if (tabs.length === 0) {
                    this.showMessage('warning', '请先打开MDAC网站');
                    // 提供快速打开选项
                    this.showModal('打开MDAC网站',
                        '需要先打开MDAC网站才能填充数据。是否现在打开？',
                        () => this.openMDACWebsite()
                    );
                    return;
                }

                // 向所有MDAC标签页发送数据
                let successCount = 0;
                for (const tab of tabs) {
                    try {
                        await chrome.tabs.sendMessage(tab.id, {
                            action: 'fillMDACForm',
                            data: allData,
                            mappings: this.fieldMappings
                        });
                        successCount++;
                    } catch (error) {
                        console.warn('发送到标签页失败:', tab.id, error);
                    }
                }

                if (successCount > 0) {
                    this.showMessage('success', `数据已发送到 ${successCount} 个MDAC页面`);
                    // 自动保存成功的数据
                    await this.saveData();
                } else {
                    this.showMessage('error', '无法发送数据到MDAC页面，请检查页面是否正确加载');
                }

            } catch (error) {
                console.error('更新到MDAC失败:', error);
                this.showMessage('error', '更新到MDAC失败: ' + error.message);
            }
        }

        /**
         * 切换城市查看器
         */
        toggleCityViewer() {
            if (this.elements.cityViewerArea) {
                const isVisible = this.elements.cityViewerArea.style.display !== 'none';
                if (isVisible) {
                    this.closeCityViewer();
                } else {
                    this.openCityViewer();
                }
            }
        }

        /**
         * 打开城市查看器
         */
        openCityViewer() {
            if (this.elements.cityViewerArea) {
                this.elements.cityViewerArea.style.display = 'block';
                this.initializeCityViewer();
                this.showMessage('info', '城市查看器已打开');
            }
        }

        /**
         * 关闭城市查看器
         */
        closeCityViewer() {
            if (this.elements.cityViewerArea) {
                this.elements.cityViewerArea.style.display = 'none';
            }
        }

        /**
         * 初始化城市查看器
         */
        initializeCityViewer() {
            if (!this.cityData || Object.keys(this.cityData).length === 0) {
                this.showMessage('warning', '城市数据未加载');
                return;
            }

            // 填充州属选择器
            this.populateStateFilter();

            // 显示所有城市
            this.displayCities();
        }

        /**
         * 填充州属过滤器
         */
        populateStateFilter() {
            if (!this.elements.stateFilter || !this.cityData) return;

            // 清空现有选项
            this.elements.stateFilter.innerHTML = '<option value="">所有州属</option>';

            // 添加州属选项
            Object.keys(this.cityData).forEach(state => {
                const option = document.createElement('option');
                option.value = state;
                option.textContent = state;
                this.elements.stateFilter.appendChild(option);
            });
        }

        /**
         * 显示城市列表
         */
        displayCities(filterState = '', searchTerm = '') {
            if (!this.elements.cityList || !this.cityData) return;

            let cities = [];

            // 收集城市数据
            Object.entries(this.cityData).forEach(([state, stateCities]) => {
                if (!filterState || state === filterState) {
                    if (Array.isArray(stateCities)) {
                        stateCities.forEach(city => {
                            cities.push({
                                name: city.name || city,
                                state: state,
                                code: city.code || '',
                                postcode: city.postcode || ''
                            });
                        });
                    }
                }
            });

            // 搜索过滤
            if (searchTerm) {
                cities = cities.filter(city =>
                    city.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    city.state.toLowerCase().includes(searchTerm.toLowerCase())
                );
            }

            // 生成HTML
            let html = '';
            cities.forEach(city => {
                html += `
                    <div class="city-item" data-city="${city.name}" data-state="${city.state}" data-code="${city.code}">
                        <div class="city-info">
                            <div class="city-name">${city.name}</div>
                            <div class="city-details">
                                <span class="city-state">${city.state}</span>
                                ${city.postcode ? `<span class="city-postcode">${city.postcode}</span>` : ''}
                            </div>
                        </div>
                        ${city.code ? `<div class="city-code">${city.code}</div>` : ''}
                    </div>
                `;
            });

            this.elements.cityList.innerHTML = html || '<div class="no-cities">未找到匹配的城市</div>';

            // 添加城市点击事件
            this.elements.cityList.querySelectorAll('.city-item').forEach(item => {
                item.addEventListener('click', () => {
                    this.selectCity(item.dataset);
                });
            });

            // 更新统计
            const statsEl = document.getElementById('cityListStats');
            if (statsEl) {
                statsEl.textContent = `共 ${cities.length} 个城市`;
            }
        }

        /**
         * 选择城市
         */
        selectCity(cityData) {
            // 填充州属和城市字段
            if (this.elements.formFields.state) {
                this.elements.formFields.state.value = cityData.state;
            }
            if (this.elements.formFields.city) {
                this.elements.formFields.city.value = cityData.city;
            }

            // 更新数据
            this.data.travel.state = cityData.state;
            this.data.travel.city = cityData.city;

            // 关闭城市查看器
            this.closeCityViewer();

            // 更新字段状态
            this.updateFieldStatus();

            this.showMessage('success', `已选择城市: ${cityData.city}, ${cityData.state}`);
        }

        /**
         * 初始化位置选择器
         */
        initializeLocationSelectors() {
            // 如果有城市数据，初始化州属选择器
            if (this.cityData && this.elements.formFields.state) {
                const stateSelect = this.elements.formFields.state;
                stateSelect.innerHTML = '<option value="">选择州属</option>';

                Object.keys(this.cityData).forEach(state => {
                    const option = document.createElement('option');
                    option.value = state;
                    option.textContent = state;
                    stateSelect.appendChild(option);
                });

                // 州属变化时更新城市选择器
                stateSelect.addEventListener('change', (e) => {
                    this.updateCitySelector(e.target.value);
                });
            }
        }

        /**
         * 更新城市选择器
         */
        updateCitySelector(selectedState) {
            if (!this.elements.formFields.city || !this.cityData) return;

            const citySelect = this.elements.formFields.city;
            citySelect.innerHTML = '<option value="">选择城市</option>';

            if (selectedState && this.cityData[selectedState]) {
                const cities = this.cityData[selectedState];
                if (Array.isArray(cities)) {
                    cities.forEach(city => {
                        const option = document.createElement('option');
                        option.value = city.name || city;
                        option.textContent = city.name || city;
                        citySelect.appendChild(option);
                    });
                }
            }
        }

        /**
         * 显示模态框
         */
        showModal(title, content, confirmCallback = null) {
            if (!this.elements.modal) return;

            this.elements.modalTitle.textContent = title;
            this.elements.modalBody.innerHTML = content;
            this.elements.modal.classList.add('show');

            // 设置确认回调
            if (confirmCallback) {
                this.elements.modalConfirm.onclick = () => {
                    confirmCallback();
                    this.hideModal();
                };
                this.elements.modalConfirm.style.display = 'block';
            } else {
                this.elements.modalConfirm.style.display = 'none';
            }
        }

        /**
         * 隐藏模态框
         */
        hideModal() {
            if (this.elements.modal) {
                this.elements.modal.classList.remove('show');
            }
        }

        /**
         * 加载保存的数据
         */
        async loadSavedData() {
            try {
                const savedData = await chrome.storage.local.get(['mdac_personal_data', 'mdac_travel_data']);

                if (savedData.mdac_personal_data) {
                    this.data.personal = savedData.mdac_personal_data;
                    this.fillPersonalFields(this.data.personal);
                }

                if (savedData.mdac_travel_data) {
                    this.data.travel = savedData.mdac_travel_data;
                    this.fillTravelFields(this.data.travel);
                }

                console.log('✅ [MDACMainController] 保存的数据加载完成');
            } catch (error) {
                console.warn('⚠️ [MDACMainController] 加载保存数据失败:', error);
            }
        }

        /**
         * 保存数据
         */
        async saveData() {
            try {
                const personalData = this.collectPersonalData();
                const travelData = this.collectTravelData();

                await chrome.storage.local.set({
                    mdac_personal_data: personalData,
                    mdac_travel_data: travelData
                });

                this.showMessage('success', '数据已保存');
            } catch (error) {
                console.error('保存数据失败:', error);
                this.showMessage('error', '保存数据失败');
            }
        }

        /**
         * 获取控制器状态
         */
        getStatus() {
            return {
                initialized: this.initialized,
                version: this.version,
                dataLoaded: {
                    aiConfig: !!this.aiConfig?.loaded,
                    cityData: !!this.cityData && Object.keys(this.cityData).length > 0,
                    fieldMappings: !!this.fieldMappings && Object.keys(this.fieldMappings).length > 0
                },
                fieldsCount: {
                    personal: Object.keys(this.data.personal).length,
                    travel: Object.keys(this.data.travel).length
                }
            };
        }

        /**
         * 自动保存数据
         */
        async autoSaveData() {
            if (this.autoSaveTimeout) {
                clearTimeout(this.autoSaveTimeout);
            }

            this.autoSaveTimeout = setTimeout(async () => {
                try {
                    await this.saveData();
                    console.log('🔄 [MDACMainController] 自动保存完成');
                } catch (error) {
                    console.warn('⚠️ [MDACMainController] 自动保存失败:', error);
                }
            }, 2000); // 2秒后自动保存
        }

        /**
         * 保存预设数据
         */
        async savePresetData() {
            try {
                const presetData = {
                    email: this.elements.presetEmail?.value || '',
                    phone: this.elements.presetPhone?.value || ''
                };

                await chrome.storage.local.set({ mdac_preset_data: presetData });
                console.log('✅ [MDACMainController] 预设数据已保存');
            } catch (error) {
                console.warn('⚠️ [MDACMainController] 预设数据保存失败:', error);
            }
        }

        /**
         * 加载预设数据
         */
        async loadPresetData() {
            try {
                const result = await chrome.storage.local.get(['mdac_preset_data']);
                if (result.mdac_preset_data) {
                    const presetData = result.mdac_preset_data;
                    if (this.elements.presetEmail) {
                        this.elements.presetEmail.value = presetData.email || '<EMAIL>';
                    }
                    if (this.elements.presetPhone) {
                        this.elements.presetPhone.value = presetData.phone || '+60167372551';
                    }
                }
            } catch (error) {
                console.warn('⚠️ [MDACMainController] 预设数据加载失败:', error);
            }
        }

        /**
         * 搜索城市
         */
        searchCities() {
            const searchTerm = this.elements.citySearchInput?.value?.trim() || '';
            const selectedState = this.elements.stateFilter?.value || '';
            this.displayCities(selectedState, searchTerm);
        }

        /**
         * 验证表单数据
         */
        validateFormData() {
            const errors = [];
            const warnings = [];

            // 验证必填字段
            const requiredFields = {
                name: '姓名',
                passportNo: '护照号码',
                dateOfBirth: '出生日期',
                nationality: '国籍',
                arrivalDate: '到达日期'
            };

            Object.entries(requiredFields).forEach(([field, label]) => {
                const element = this.elements.formFields[field];
                if (!element || !element.value || !element.value.trim()) {
                    errors.push(`${label}为必填项`);
                }
            });

            // 验证日期格式
            const dateFields = ['dateOfBirth', 'arrivalDate', 'departureDate', 'passportExpiry'];
            dateFields.forEach(field => {
                const element = this.elements.formFields[field];
                if (element && element.value) {
                    const dateValue = new Date(element.value);
                    if (isNaN(dateValue.getTime())) {
                        errors.push(`${this.getFieldLabel(field)}日期格式不正确`);
                    }
                }
            });

            // 验证护照号格式
            const passportElement = this.elements.formFields.passportNo;
            if (passportElement && passportElement.value) {
                const passportPattern = /^[A-Z0-9]{6,12}$/;
                if (!passportPattern.test(passportElement.value)) {
                    warnings.push('护照号格式可能不正确');
                }
            }

            return { errors, warnings, isValid: errors.length === 0 };
        }

        /**
         * 显示验证结果
         */
        showValidationResult(validation) {
            if (validation.errors.length > 0) {
                this.showMessage('error', `验证失败: ${validation.errors.join(', ')}`);
                return false;
            }

            if (validation.warnings.length > 0) {
                this.showMessage('warning', `注意: ${validation.warnings.join(', ')}`);
            }

            return true;
        }

        /**
         * 调试信息
         */
        debug() {
            console.log('🐛 [MDACMainController] 调试信息:');
            console.log('状态:', this.getStatus());
            console.log('个人数据:', this.data.personal);
            console.log('旅行数据:', this.data.travel);
            console.log('UI元素:', this.elements);
            console.log('配置数据:', {
                aiConfig: this.aiConfig,
                cityDataKeys: this.cityData ? Object.keys(this.cityData) : null,
                fieldMappings: this.fieldMappings
            });
        }
    }
    }

    // 创建全局实例
    window.MDACMainController = MDACMainController;
    window.mdacController = new MDACMainController();

    // 自动初始化
    window.mdacController.initialize().catch(error => {
        console.error('❌ [MDACMainController] 自动初始化失败:', error);
    });

})();
